
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useScheduleValidation = () => {
  // Buscar indisponibilidades dos membros
  const { data: memberUnavailabilities } = useQuery({
    queryKey: ['member-unavailabilities-all'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('disponibilidade_membros')
        .select('*')
        .gte('data_indisponivel', new Date().toISOString().split('T')[0]);

      if (error) {
        console.error('Error fetching member unavailabilities:', error);
        return [];
      }

      console.log(`📊 Carregadas ${data?.length || 0} indisponibilidades:`, data);
      return data || [];
    }
  });

  // Função para verificar se um membro está disponível em uma data específica
  const isMemberAvailableOnDate = (memberName: string, date: string): boolean => {
    if (!memberUnavailabilities) return true;

    // Converter data para formato YYYY-MM-DD se necessário
    const checkDate = typeof date === 'string' && date.includes('/')
      ? date.split('/').reverse().join('-')
      : date;

    // Debug: log para verificar se há indisponibilidades carregadas
    if (memberUnavailabilities.length === 0) {
      console.log('⚠️ Nenhuma indisponibilidade carregada');
      return true;
    }

    // Verificar se há indisponibilidade para este membro nesta data
    const hasUnavailability = memberUnavailabilities.some(
      unavail => {
        const isMatch = unavail.membro_nome === memberName && unavail.data_indisponivel === checkDate;

        // Debug: log detalhado apenas para a data que estamos verificando
        if (unavail.data_indisponivel === checkDate) {
          console.log(`🔍 Verificando: "${memberName}" vs "${unavail.membro_nome}" em ${checkDate}: ${isMatch ? 'INDISPONÍVEL' : 'OK'}`);
        }

        return isMatch;
      }
    );

    return !hasUnavailability;
  };

  // Função para filtrar membros disponíveis baseado na data
  const filterAvailableMembers = (members: any[], date: string) => {
    return members.filter(member => {
      const memberName = member['Nome Escala'] || member.Nome || member.name;
      return isMemberAvailableOnDate(memberName, date);
    });
  };

  return {
    memberUnavailabilities,
    isMemberAvailableOnDate,
    filterAvailableMembers
  };
};
