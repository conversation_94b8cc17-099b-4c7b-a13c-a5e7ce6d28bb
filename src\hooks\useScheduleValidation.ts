
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useScheduleValidation = () => {
  // Buscar indisponibilidades dos membros
  const { data: memberUnavailabilities } = useQuery({
    queryKey: ['member-unavailabilities-all'],
    queryFn: async () => {
      const today = new Date().toISOString().split('T')[0];
      console.log(`🗓️ Buscando indisponibilidades a partir de: ${today}`);

      const { data, error } = await supabase
        .from('disponibilidade_membros')
        .select('*')
        .gte('data_indisponivel', today);

      // Vamos também buscar TODOS os registros para debug
      const { data: allData, error: allError } = await supabase
        .from('disponibilidade_membros')
        .select('*');

      console.log(`🔍 Total de registros na tabela: ${allData?.length || 0}`, allData);
      console.log(`🔍 Registros filtrados (>= ${today}): ${data?.length || 0}`, data);

      if (error) {
        console.error('Error fetching member unavailabilities:', error);
        return [];
      }

      console.log(`📊 Carregadas ${data?.length || 0} indisponibilidades:`, data);

      return data || [];
    }
  });

  // Função para verificar se um membro está disponível em uma data específica
  const isMemberAvailableOnDate = (memberName: string, date: string): boolean => {
    if (!memberUnavailabilities) return true;

    // Converter data para formato YYYY-MM-DD se necessário
    const checkDate = typeof date === 'string' && date.includes('/')
      ? date.split('/').reverse().join('-')
      : date;

    // Verificar se há indisponibilidade para este membro nesta data
    const hasUnavailability = memberUnavailabilities.some(
      unavail => {
        const isMatch = unavail.membro_nome === memberName && unavail.data_indisponivel === checkDate;

        // Debug log detalhado
        if (unavail.data_indisponivel === checkDate) {
          console.log(`🔍 Verificando ${memberName} vs ${unavail.membro_nome} em ${checkDate}: ${isMatch ? 'MATCH' : 'NO MATCH'}`);
        }

        return isMatch;
      }
    );

    // Debug log para verificar o que está acontecendo
    if (hasUnavailability) {
      console.log(`🚫 Membro ${memberName} está indisponível em ${checkDate}`);
    }

    return !hasUnavailability;
  };

  // Função para filtrar membros disponíveis baseado na data
  const filterAvailableMembers = (members: any[], date: string) => {
    return members.filter(member => {
      const memberName = member['Nome Escala'] || member.Nome || member.name;
      return isMemberAvailableOnDate(memberName, date);
    });
  };

  return {
    memberUnavailabilities,
    isMemberAvailableOnDate,
    filterAvailableMembers
  };
};
