
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const useScheduleValidation = () => {
  // Buscar TODAS as indisponibilidades (sem filtro de data)
  const { data: memberUnavailabilities } = useQuery({
    queryKey: ['member-unavailabilities-all'],
    queryFn: async () => {
      console.log('🔍 Buscando TODAS as indisponibilidades...');

      try {
        // Tentar definir contexto administrativo primeiro
        console.log('🔧 Tentando definir contexto administrativo...');
        const { error: rpcError } = await supabase.rpc('set_current_member', { member_name: 'ADMIN' });
        if (rpcError) {
          console.log('⚠️ Erro ao definir contexto admin:', rpcError);
        } else {
          console.log('✅ Contexto administrativo definido');
        }

        // Buscar TODOS os registros (sem filtro de data)
        const { data, error } = await supabase
          .from('disponibilidade_membros')
          .select('*');

        console.log('🔍 Resultado busca TODAS as indisponibilidades:', {
          count: data?.length || 0,
          error: error,
          data: data
        });

        if (error) {
          console.error('❌ Erro ao buscar indisponibilidades:', error);
          return [];
        }

        console.log(`📊 Carregadas ${data?.length || 0} indisponibilidades total`);
        return data || [];

      } catch (err) {
        console.error('❌ Erro geral na busca:', err);
        return [];
      }
    }
  });

  // Função para verificar se um membro está disponível em uma data específica
  const isMemberAvailableOnDate = (memberName: string, date: string): boolean => {
    console.log(`🔍 Verificando disponibilidade: "${memberName}" em ${date}`);

    if (!memberUnavailabilities) {
      console.log('⚠️ memberUnavailabilities é null/undefined');
      return true;
    }

    // Converter data para formato YYYY-MM-DD se necessário
    const checkDate = typeof date === 'string' && date.includes('/')
      ? date.split('/').reverse().join('-')
      : date;

    console.log(`📅 Data convertida: ${date} → ${checkDate}`);

    // Debug: log para verificar se há indisponibilidades carregadas
    if (memberUnavailabilities.length === 0) {
      console.log('⚠️ Nenhuma indisponibilidade carregada - array vazio');
      return true;
    }

    console.log(`📊 Total de indisponibilidades carregadas: ${memberUnavailabilities.length}`);

    // Mostrar todas as indisponibilidades para esta data
    const unavailabilitiesForDate = memberUnavailabilities.filter(unavail => unavail.data_indisponivel === checkDate);
    console.log(`📅 Indisponibilidades para ${checkDate}:`, unavailabilitiesForDate);

    // Verificar se há indisponibilidade para este membro nesta data
    const hasUnavailability = memberUnavailabilities.some(
      unavail => {
        const nameMatch = unavail.membro_nome === memberName;
        const dateMatch = unavail.data_indisponivel === checkDate;
        const isMatch = nameMatch && dateMatch;

        // Debug: log detalhado para cada comparação
        if (dateMatch) {
          console.log(`🔍 Comparando nomes: "${memberName}" === "${unavail.membro_nome}" = ${nameMatch}`);
          console.log(`🔍 Comparando datas: "${checkDate}" === "${unavail.data_indisponivel}" = ${dateMatch}`);
          console.log(`🔍 Resultado final: ${isMatch ? 'INDISPONÍVEL ❌' : 'DISPONÍVEL ✅'}`);
        }

        return isMatch;
      }
    );

    const result = !hasUnavailability;
    console.log(`🎯 Resultado final para ${memberName}: ${result ? 'DISPONÍVEL ✅' : 'INDISPONÍVEL ❌'}`);

    return result;
  };

  // Função para filtrar membros disponíveis baseado na data
  const filterAvailableMembers = (members: any[], date: string) => {
    return members.filter(member => {
      const memberName = member['Nome Escala'] || member.Nome || member.name;
      return isMemberAvailableOnDate(memberName, date);
    });
  };

  return {
    memberUnavailabilities,
    isMemberAvailableOnDate,
    filterAvailableMembers
  };
};
