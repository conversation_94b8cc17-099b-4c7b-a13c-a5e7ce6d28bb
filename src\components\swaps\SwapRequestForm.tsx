
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowRightLeft, AlertTriangle } from 'lucide-react';
import { ScheduleItem, AvailableMember } from '@/types/swapTypes';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { debouncedInvalidateMultipleQueries } from '@/utils/cacheUtils';

interface SwapRequestFormProps {
  memberSchedules: ScheduleItem[] | undefined;
  onCreateSwap: (scheduleId: string, reason: string) => void;
  isCreating: boolean;
  initialScheduleId?: string | null;
}

export const SwapRequestForm = ({ memberSchedules, onCreateSwap, isCreating, initialScheduleId }: SwapRequestFormProps) => {
  const queryClient = useQueryClient();
  const [selectedSchedule, setSelectedSchedule] = useState<string>('');
  const [reason, setReason] = useState('');

  useEffect(() => {
    if (initialScheduleId) {
      setSelectedSchedule(initialScheduleId);
    }
  }, [initialScheduleId]);

  // Atualizar dados quando o componente for renderizado (mudança de aba)
  useEffect(() => {
    // Não precisamos invalidar aqui pois os dados já vêm como props
    // A invalidação será feita na página principal
  }, []); // Executar apenas uma vez quando o componente for montado

  // Filtrar apenas escalas futuras
  const futureSchedules = memberSchedules?.filter(schedule => {
    if (!schedule.Data) return false;
    const dateParts = schedule.Data.split('/');
    if (dateParts.length !== 3) return false;
    const scheduleDate = new Date(`${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`);
    return scheduleDate >= new Date();
  }) || [];

  const selectedScheduleData = futureSchedules.find(s => s.ID === selectedSchedule);

  const validateScheduleSelection = () => {
    if (!selectedScheduleData) return true;
    
    // Verificar se a data é válida e futura
    const dateParts = selectedScheduleData.Data.split('/');
    if (dateParts.length !== 3) {
      toast.error('Data da escala inválida');
      return false;
    }
    
    const scheduleDate = new Date(`${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`);
    if (isNaN(scheduleDate.getTime())) {
      toast.error('Data da escala inválida');
      return false;
    }
    
    if (scheduleDate < new Date()) {
      toast.error('Não é possível solicitar troca para escalas passadas');
      return false;
    }
    
    return true;
  };

  const handleSubmit = () => {
    if (!validateScheduleSelection()) return;
    
    onCreateSwap(selectedSchedule, reason);
    setSelectedSchedule('');
    setReason('');
  };

  const isFormValid = selectedSchedule && reason.trim();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ArrowRightLeft className="w-5 h-5" />
          Nova Solicitação de Troca
        </CardTitle>
        <CardDescription>
          Solicite a troca de uma de suas escalas futuras
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">
            Selecione a Escala
          </label>
          <Select value={selectedSchedule} onValueChange={setSelectedSchedule}>
            <SelectTrigger>
              <SelectValue placeholder="Escolha uma escala futura..." />
            </SelectTrigger>
            <SelectContent>
              {futureSchedules.length > 0 ? (
                futureSchedules.map((schedule) => {
                  const isValidDate = schedule.Data && 
                    schedule.Data.split('/').length === 3 && 
                    !isNaN(new Date(`${schedule.Data.split('/')[2]}-${schedule.Data.split('/')[1]}-${schedule.Data.split('/')[0]}`).getTime());
                  
                  return (
                    <SelectItem key={schedule.ID} value={schedule.ID}>
                      {isValidDate 
                        ? `${schedule.Data} - ${schedule.Turno} (${schedule.Dia})`
                        : `${schedule.Data || 'Data inválida'} - ${schedule.Turno}`
                      }
                    </SelectItem>
                  );
                })
              ) : (
                <SelectItem value="none" disabled>
                  Nenhuma escala futura encontrada
                </SelectItem>
              )}
            </SelectContent>
          </Select>
          {futureSchedules.length === 0 && (
            <div className="flex items-center gap-2 mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded">
              <AlertTriangle className="w-4 h-4 text-yellow-600" />
              <span className="text-sm text-yellow-700">
                Você não possui escalas futuras disponíveis para troca
              </span>
            </div>
          )}
        </div>


        <div>
          <label className="block text-sm font-medium mb-2">
            Motivo da Troca *
          </label>
          <Textarea
            placeholder="Explique o motivo da solicitação de troca..."
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            required
          />
        </div>

        <Button 
          onClick={handleSubmit}
          disabled={isCreating || !isFormValid}
          className="w-full"
        >
          {isCreating ? 'Criando...' : 'Criar Solicitação'}
        </Button>
      </CardContent>
    </Card>
  );
};
