import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Clock, CheckCircle, XCircle, Users, AlertCircle, Ban } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { SwapRequest } from '@/types/swapTypes';

interface RequestCounts {
  pendente: number;
  aguardando_candidatos: number;
  aguardando_aprovacao: number;
  aceita: number;
  rejeitada: number;
  cancelada: number;
}

interface Props {
  memberName: string | undefined;
}

export const UserSwapRequestsSummary = ({ memberName }: Props) => {
  const queryClient = useQueryClient();

  // Buscar todas as solicitações do usuário (histórico completo)
  const { data: userRequests, isLoading, refetch } = useQuery({
    queryKey: ['user-all-swap-requests', memberName],
    queryFn: async (): Promise<SwapRequest[]> => {
      if (!memberName) return [];
      
      console.log('Fetching all swap requests for member:', memberName);
      
      await supabase.rpc('set_current_member', { member_name: memberName });
      
      const { data, error } = await supabase
        .from('solicitacoes_troca')
        .select('*')
        .eq('id_solicitante', memberName)
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error('Error fetching user swap requests:', error);
        return [];
      }

      // Validar e converter os dados para o tipo SwapRequest
      return (data || []).map(item => ({
        id: item.id,
        id_solicitante: item.id_solicitante,
        data_solicitacao: item.data_solicitacao,
        mensagem: item.mensagem || '',
        status: item.status === 'pendente' ||
                item.status === 'aguardando_candidatos' ||
                item.status === 'aguardando_aprovacao' ||
                item.status === 'aceita' ||
                item.status === 'rejeitada' ||
                item.status === 'cancelada'
                ? item.status
                : 'pendente', // Default para pendente se inválido
        created_at: item.created_at,
        updated_at: item.updated_at,
        data_resposta: item.data_resposta || undefined,
        id_escala_origem: item.id_escala_origem,
        id_escala_destino: item.id_escala_destino || undefined,
        membro_destino: item.membro_destino || undefined,
        tipo_solicitacao: item.tipo_solicitacao || 'troca',
        candidato_selecionado: item.candidato_selecionado || undefined,
        data_selecao_candidato: item.data_selecao_candidato || undefined,
        observacoes_admin: item.observacoes_admin || undefined,
        responsavel_aprovacao: item.responsavel_aprovacao || undefined
      }));
    },
    enabled: !!memberName,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 15 * 60 * 1000, // 15 minutos
    refetchOnWindowFocus: false // Evitar refresh automático ao focar na janela
  });

  const handleRefresh = () => {
    refetch();
    queryClient.invalidateQueries({ queryKey: ['user-all-swap-requests'] });
  };

  const requestCounts = React.useMemo(() => {
    const counts: RequestCounts = {
      pendente: 0,
      aguardando_candidatos: 0,
      aguardando_aprovacao: 0,
      aceita: 0,
      rejeitada: 0,
      cancelada: 0
    };

    if (userRequests) {
      userRequests.forEach(request => {
        counts[request.status]++;
      });
    }

    return counts;
  }, [userRequests]);

  const getStatusConfig = (status: keyof RequestCounts) => {
    switch (status) {
      case 'pendente':
        return {
          label: 'Pendentes',
          icon: Clock,
          variant: 'secondary' as const,
          color: 'text-yellow-600'
        };
      case 'aguardando_candidatos':
        return {
          label: 'Aguardando Voluntários',
          icon: Users,
          variant: 'outline' as const,
          color: 'text-blue-600'
        };
      case 'aguardando_aprovacao':
        return {
          label: 'Aguardando Aprovação',
          icon: AlertCircle,
          variant: 'secondary' as const,
          color: 'text-orange-600'
        };
      case 'aceita':
        return {
          label: 'Aprovadas',
          icon: CheckCircle,
          variant: 'default' as const,
          color: 'text-green-600'
        };
      case 'rejeitada':
        return {
          label: 'Rejeitadas',
          icon: XCircle,
          variant: 'destructive' as const,
          color: 'text-red-600'
        };
      case 'cancelada':
        return {
          label: 'Canceladas',
          icon: Ban,
          variant: 'outline' as const,
          color: 'text-gray-600'
        };
    }
  };

  if (isLoading) {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Suas Solicitações</CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
          </div>
        </CardContent>
      </Card>
    );
  }

  const totalRequests = Object.values(requestCounts).reduce((sum, count) => sum + count, 0);

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg flex items-center justify-between">
          Suas Solicitações
          <div className="flex gap-2">
            <Button variant="ghost" size="sm" onClick={handleRefresh}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Atualizar
            </Button>
            <Link to="/membro-trocas">
              <Button variant="ghost" size="sm">
                Ver todas
              </Button>
            </Link>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        {totalRequests === 0 ? (
          <div className="text-center py-6">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">Nenhuma solicitação encontrada</p>
          </div>
        ) : (
          <div className="space-y-2">
            {(Object.keys(requestCounts) as Array<keyof RequestCounts>).map((status) => {
              const count = requestCounts[status];
              const config = getStatusConfig(status);
              const Icon = config.icon;

              if (count === 0) return null;

              return (
                <div key={status} className="flex items-center justify-between p-3 rounded-lg border bg-gray-50/50 hover:bg-gray-100/50 transition-colors">
                  <div className="flex items-center gap-3">
                    <Icon className={`w-4 h-4 ${config.color}`} />
                    <span className="text-sm font-medium">{config.label}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-lg font-bold">{count}</span>
                    <Badge variant={config.variant} className="text-xs">
                      {count === 1 ? 'solicitação' : 'solicitações'}
                    </Badge>
                  </div>
                </div>
              );
            })}
          </div>
        )}
        
        {totalRequests > 0 && (
          <div className="mt-4 pt-3 border-t text-center">
            <p className="text-sm text-gray-600">
              Total: <span className="font-semibold">{totalRequests}</span> solicitações
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
