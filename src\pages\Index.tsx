import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Users, BarChart3, <PERSON><PERSON><PERSON>, UserCheck, Shield, Clock, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <header className="bg-white/90 backdrop-blur-sm shadow-sm border-b border-blue-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-6 gap-4">
            <div className="flex items-center">
              <div className="flex items-center gap-4">
                <img
                  src="https://i.postimg.cc/SKRKFZvW/IMG-20230218-WA0027-removebg-preview.png"
                  alt="Brasão Ministério Sentinela"
                  className="h-12 w-12"
                />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Ministério Sentinela</h1>
                  <p className="text-sm text-blue-600 font-medium">Sistema de Gerenciamento de Escalas</p>
                </div>
              </div>
            </div>
            <div className="flex gap-3">
              <Link to="/membro-login">
                <Button variant="outline" size="sm" className="border-blue-200 text-blue-700 hover:bg-blue-50">
                  <UserCheck className="w-4 h-4 mr-2" />
                  Portal do Membro
                </Button>
              </Link>
              <Link to="/login">
                <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                  <Users className="w-4 h-4 mr-2" />
                  Acesso Administrativo
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-8">
            <img
              src="https://i.postimg.cc/SKRKFZvW/IMG-20230218-WA0027-removebg-preview.png"
              alt="Brasão Ministério Sentinela"
              className="h-24 w-24 drop-shadow-lg"
            />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Sistema de Gerenciamento
            <span className="block text-blue-600">de Escalas</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Plataforma completa para organizar escalas, gerenciar membros e acompanhar
            relatórios de forma eficiente e moderna.
          </p>
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-16">
          <div className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-blue-100">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Clock className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Gestão Eficiente</h3>
            <p className="text-gray-600">Organize escalas de forma rápida e intuitiva</p>
          </div>
          <div className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-green-100">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <Users className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Controle Total</h3>
            <p className="text-gray-600">Gerencie membros e suas disponibilidades</p>
          </div>
          <div className="text-center p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-purple-100">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Relatórios Detalhados</h3>
            <p className="text-gray-600">Acompanhe métricas e performance da equipe</p>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
          <Card className="border-blue-200 bg-white/80 backdrop-blur-sm hover:shadow-lg hover:border-blue-300 transition-all duration-300">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center mb-3">
                <Calendar className="w-6 h-6 text-blue-600" />
              </div>
              <CardTitle className="text-lg text-gray-900">
                Gerenciamento de Escalas
              </CardTitle>
              <CardDescription className="text-gray-600">
                Crie, edite e visualize escalas de serviço
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm leading-relaxed">
                Sistema completo para organizar escalas de trabalho, com calendário interativo e
                funcionalidades de edição em tempo real.
              </p>
            </CardContent>
          </Card>

          <Card className="border-green-200 bg-white/80 backdrop-blur-sm hover:shadow-lg hover:border-green-300 transition-all duration-300">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center mb-3">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <CardTitle className="text-lg text-gray-900">
                Controle de Membros
              </CardTitle>
              <CardDescription className="text-gray-600">
                Gerencie informações dos membros da equipe
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm leading-relaxed">
                Cadastro completo de membros com informações pessoais, disponibilidade e
                histórico de participação nas escalas.
              </p>
            </CardContent>
          </Card>

          <Card className="border-purple-200 bg-white/80 backdrop-blur-sm hover:shadow-lg hover:border-purple-300 transition-all duration-300">
            <CardHeader className="pb-4">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center mb-3">
                <BarChart3 className="w-6 h-6 text-purple-600" />
              </div>
              <CardTitle className="text-lg text-gray-900">
                Relatórios e Dashboard
              </CardTitle>
              <CardDescription className="text-gray-600">
                Acompanhe estatísticas e gere relatórios
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm leading-relaxed">
                Dashboard completo com métricas de participação, relatórios detalhados e
                análises de desempenho da equipe.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Access Options */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
          <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white hover:shadow-xl hover:scale-105 transition-all duration-300">
            <CardHeader className="pb-4">
              <div className="w-14 h-14 bg-blue-600 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                <UserCheck className="w-7 h-7 text-white" />
              </div>
              <CardTitle className="text-xl text-gray-900">Portal do Membro</CardTitle>
              <CardDescription className="text-blue-600 font-medium">
                Acesso para membros da equipe
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="font-medium">Visualizar suas escalas</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="font-medium">Gerenciar disponibilidade</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="font-medium">Solicitar trocas de serviços</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="font-medium">Atualizar informações pessoais</span>
                </li>
              </ul>
              <Link to="/membro-login">
                <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white shadow-md">
                  <UserCheck className="w-4 h-4 mr-2" />
                  Acessar Portal do Membro
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="border-indigo-200 bg-gradient-to-br from-indigo-50 to-white hover:shadow-xl hover:scale-105 transition-all duration-300">
            <CardHeader className="pb-4">
              <div className="w-14 h-14 bg-indigo-600 rounded-xl flex items-center justify-center mb-4 shadow-lg">
                <Settings className="w-7 h-7 text-white" />
              </div>
              <CardTitle className="text-xl text-gray-900">Área Administrativa</CardTitle>
              <CardDescription className="text-indigo-600 font-medium">
                Acesso para administradores
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                  <span className="font-medium">Criar e gerenciar escalas</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                  <span className="font-medium">Administrar membros</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                  <span className="font-medium">Visualizar relatórios</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-indigo-500 rounded-full"></div>
                  <span className="font-medium">Configurações do sistema</span>
                </li>
              </ul>
              <Link to="/login">
                <Button variant="outline" className="w-full border-indigo-300 text-indigo-700 hover:bg-indigo-50 shadow-md">
                  <Settings className="w-4 h-4 mr-2" />
                  Acesso Administrativo
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="flex items-center justify-center gap-3 mb-6">
              <img
                src="https://i.postimg.cc/SKRKFZvW/IMG-20230218-WA0027-removebg-preview.png"
                alt="Brasão Ministério Sentinela"
                className="h-10 w-10 drop-shadow-lg"
              />
              <span className="text-xl font-bold text-white">Ministério Sentinela</span>
            </div>
            <p className="text-blue-100 mb-3 text-lg">
              Sistema de Gerenciamento de Escalas
            </p>
            <p className="text-blue-200 mb-4">
              Desenvolvido para organizar e facilitar o trabalho da equipe
            </p>
            <p className="text-sm text-blue-300">
              &copy; 2024 Ministério Sentinela. Todos os direitos reservados.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
