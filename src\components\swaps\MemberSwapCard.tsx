import React, { useState, useContext } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, User, X } from 'lucide-react';
import { SwapRequest } from '@/types/swapTypes';
import { format, parseISO } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { MemberSwapCardContext } from '@/contexts/MemberSwapCardContext';

interface MemberSwapCardProps {
  request: SwapRequest;
  candidatesCount?: number;
  onCancel?: () => void | Promise<void>;
}

const formatDateBR = (dateString: string) => {
  try {
    const date = parseISO(dateString);
    return format(date, 'dd/MM/yyyy', { locale: ptBR });
  } catch {
    return dateString;
  }
};

const getStatusBadge = (status: string) => {
  switch (status) {
    case 'aguardando_candidatos':
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Aguardand<PERSON></Badge>;
    case 'aguardando_aprovacao':
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Em Análise</Badge>;
    case 'aceita':
      return <Badge variant="secondary" className="bg-green-100 text-green-800">Aprovada</Badge>;
    case 'rejeitada':
      return <Badge variant="secondary" className="bg-red-100 text-red-800">Rejeitada</Badge>;
    case 'cancelada':
      return <Badge variant="secondary">Cancelada</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

export const MemberSwapCard = ({
  request,
  candidatesCount = 0,
  onCancel,
}: MemberSwapCardProps) => {
  const { expandedId, setExpandedId } = useContext(MemberSwapCardContext);
  const isOpen = expandedId === request.id;
  const showCancelButton = request.status === 'aguardando_candidatos' || request.status === 'aguardando_aprovacao';
  const [isCanceling, setIsCanceling] = useState(false);
  const [cancelError, setCancelError] = useState<string | null>(null);

  const handleToggle = (e: React.MouseEvent) => {
    // Previne o toggle se o clique foi em um botão ou elemento interativo
    if ((e.target as HTMLElement).closest('button')) {
      return;
    }
    setExpandedId(isOpen ? null : request.id);
  };

  return (
    <Card className="border-border cursor-pointer" onClick={handleToggle}>
      <CardContent className="p-4">
        {/* Seção Pai - Informações Resumidas */}
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2 min-w-0">
            <Calendar className="w-4 h-4 text-primary flex-shrink-0" />
            <div className="min-w-0">
              <div className="flex items-baseline gap-1 truncate">
                <span className="truncate">
                  {formatDateBR(request.scheduleData?.Data)} • {request.scheduleData?.Turno}
                </span>
                <span className="text-xs text-muted-foreground shrink-0">({request.scheduleData?.Dia})</span>
              </div>
              {!isOpen && request.mensagem && (
                <div className="text-xs text-muted-foreground truncate">
                  {request.mensagem}
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2 shrink-0">
            {getStatusBadge(request.status)}
            <div className={`transition-transform ${isOpen ? 'rotate-180' : ''}`}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>

        {/* Seção Detalhada - Apenas quando expandido */}
        {isOpen && (
          <Card className="border-border mt-4">
            <CardContent className="p-4 space-y-2">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <div className="text-sm font-medium">Data solicitação</div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>{formatDateBR(request.data_solicitacao)}</span>
                  </div>
                </div>
                <div className="space-y-1">
                  <div className="text-sm font-medium">Candidaturas</div>
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    <span>{candidatesCount || 0}</span>
                  </div>
                </div>
              </div>

              {request.mensagem && (
                <div className="space-y-1">
                  <div className="text-sm font-medium">Motivo</div>
                  <div className="text-sm text-muted-foreground">
                    {request.mensagem}
                  </div>
                </div>
              )}

              {showCancelButton && onCancel && (
                <div className="flex justify-end pt-4">
                  {cancelError && (
                    <div className="text-red-500 text-sm">{cancelError}</div>
                  )}
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={async (e) => {
                      e.stopPropagation(); // Previne a propagação do evento
                      try {
                        setIsCanceling(true);
                        setCancelError(null);
                        await onCancel();
                      } catch (error) {
                        setCancelError('Falha ao cancelar solicitação. Tente novamente.');
                        console.error('Cancel error:', error);
                      } finally {
                        setIsCanceling(false);
                      }
                    }}
                    className="flex items-center gap-2"
                    disabled={isCanceling}
                  >
                    {isCanceling ? (
                      <>
                        <span className="animate-spin">↻</span>
                        Cancelando...
                      </>
                    ) : (
                      <>
                        <X className="w-4 h-4" />
                        Cancelar Solicitação
                      </>
                    )}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
      )}
      </CardContent>
    </Card>
  );
};