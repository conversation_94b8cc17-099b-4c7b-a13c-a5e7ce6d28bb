
import React from 'react';
import { Card } from '@/components/ui/card';
import { Calendar, RefreshCw, Users, Clock, CheckCircle } from 'lucide-react';

interface DashboardStatsProps {
  upcomingSchedulesCount: number;
  swapRequestsCount: number;
  swapsWithCandidatesCount?: number;
  swapsAwaitingCandidatesCount?: number;
  birthdaysCount: number;
  memberStatus: string;
  adminView?: boolean;
}

export const DashboardStats = ({ 
  upcomingSchedulesCount, 
  swapRequestsCount, 
  swapsWithCandidatesCount,
  swapsAwaitingCandidatesCount,
  birthdaysCount, 
  memberStatus,
  adminView = false
}: DashboardStatsProps) => {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Calendar className="h-5 w-5 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Próximas Escalas</p>
              <p className="text-2xl font-bold">{upcomingSchedulesCount}</p>
            </div>
          </div>
        </div>
      </Card>

      {adminView ? (
        <>
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Pendentes de Aprovação</p>
                  <p className="text-2xl font-bold text-blue-600">{swapsWithCandidatesCount || 0}</p>
                </div>
              </div>
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Clock className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Aguardando Voluntários</p>
                  <p className="text-2xl font-bold text-yellow-600">{swapsAwaitingCandidatesCount || 0}</p>
                </div>
              </div>
            </div>
          </Card>
        </>
      ) : (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <RefreshCw className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Solicitações de Troca</p>
                <p className="text-2xl font-bold">{swapRequestsCount}</p>
              </div>
            </div>
          </div>
        </Card>
      )}

      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Users className="h-5 w-5 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium text-muted-foreground">Aniversariantes</p>
              <p className="text-2xl font-bold">{birthdaysCount}</p>
            </div>
          </div>
        </div>
      </Card>

    </div>
  );
};
