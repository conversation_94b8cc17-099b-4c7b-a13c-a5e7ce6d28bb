import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { Bell, Shield, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useMemberAuth } from '@/contexts/MemberAuthContext';
import { useMemberDashboardData } from '@/hooks/useMemberDashboardData';
import { useAdminPermissions } from '@/hooks/useAdminPermissions';

export const MemberNotificationBell = () => {
  const navigate = useNavigate();
  const { auth } = useMemberAuth();
  const { hasAdminAccess } = useAdminPermissions();
  const memberName = auth.member?.['Nome Escala'];
  const [isOpen, setIsOpen] = useState(false);

  // Buscar dados administrativos apenas se tiver permissão
  const {
    totalSwapRequestsCount,
    swapsWithCandidatesCount
  } = useMemberDashboardData('admin', hasAdminAccess);

  // Calcular total de notificações
  const totalNotifications = hasAdminAccess ? (totalSwapRequestsCount || 0) : 0;
  const urgentNotifications = hasAdminAccess ? (swapsWithCandidatesCount || 0) : 0;

  const handleAdminSwapClick = () => {
    navigate('/admin-swap-requests');
    setIsOpen(false);
  };

  if (!hasAdminAccess || totalNotifications === 0) {
    return (
      <Button variant="ghost" size="sm" className="relative">
        <Bell className="h-5 w-5 text-muted-foreground" />
      </Button>
    );
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-5 w-5 text-muted-foreground" />
          {totalNotifications > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {totalNotifications > 99 ? '99+' : totalNotifications}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex items-center gap-2">
          <Shield className="h-4 w-4 text-blue-600" />
          Notificações Administrativas
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={handleAdminSwapClick}
          className="flex items-center justify-between p-3 cursor-pointer"
        >
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
            <div>
              <p className="font-medium text-sm">Solicitações de Troca</p>
              <p className="text-xs text-muted-foreground">
                {totalSwapRequestsCount} pendentes
                {urgentNotifications > 0 && `, ${urgentNotifications} prontas para aprovação`}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {urgentNotifications > 0 && (
              <Badge variant="destructive" className="text-xs">
                Urgente
              </Badge>
            )}
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
          </div>
        </DropdownMenuItem>

        <DropdownMenuSeparator />
        <DropdownMenuItem 
          onClick={handleAdminSwapClick}
          className="text-center text-sm text-blue-600 hover:text-blue-700"
        >
          Ver todas as notificações
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
