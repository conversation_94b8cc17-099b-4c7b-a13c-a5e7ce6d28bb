
import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar } from 'lucide-react';

interface Schedule {
  ID: string;
  Data: string;
  Turno: string;
  Dia: string;
  Lider: string;
  Membro1?: string;
  Membro2?: string;
  Membro3?: string;
  Membro4?: string;
}

interface UpcomingSchedulesProps {
  schedules: Schedule[] | undefined;
  memberName: string;
}

export const UpcomingSchedules = ({ schedules, memberName }: UpcomingSchedulesProps) => {
  return (
    <Card className="p-4">
      <div className="mb-4">
        <h3 className="text-lg font-medium">Próximas Escalas</h3>
        <p className="text-sm text-muted-foreground">Suas próximas atividades programadas</p>
      </div>
      <div>
        {schedules && schedules.length > 0 ? (
          <div className="space-y-2">
            {schedules.map((schedule, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <div>
                    <p className="font-medium text-sm">{schedule.Data} - {schedule.Turno}</p>
                    <p className="text-xs text-muted-foreground">{schedule.Dia}</p>
                  </div>
                </div>
                <Badge variant="outline" className="text-xs">
                  {schedule.Lider === memberName ? 'Líder' : 'Membro'}
                </Badge>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6">
            <Calendar className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-500">Nenhuma escala programada</p>
          </div>
        )}
      </div>
    </Card>
  );
};
