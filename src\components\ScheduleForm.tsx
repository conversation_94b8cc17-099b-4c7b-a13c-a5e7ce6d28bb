import React, { useState, useEffect, useRef } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useSchedule } from '@/contexts/ScheduleContext';
import { fetchPersons } from '@/services/api';
import { getDayOfWeek } from '@/utils/dateUtils';
import { Person, DayOfWeek } from '@/types';
import { format, parse } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { MultiSelect } from '@/components/MultiSelect';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { DndContext, DragEndEvent, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext, arrayMove, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Loading } from '@/components/ui/loading';
import { useLocation } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
// Define types locally since they're not exported from supabaseTypes
type ServiceFrequencyStatus = 'above' | 'average' | 'below';
interface MemberServiceStats {
  count: number;
  status: ServiceFrequencyStatus;
}
import { useScheduleValidation } from '@/hooks/useScheduleValidation';

// SortableItem component for drag and drop
interface SortableItemProps {
  id: string;
  name: string;
  isHighlighted: boolean;
  serviceCount?: number;
  serviceStatus?: ServiceFrequencyStatus;
  onRemove: () => void;
}

const SortableItem: React.FC<SortableItemProps> = ({ 
  id, 
  name, 
  isHighlighted, 
  serviceCount = 0,
  serviceStatus = 'average', 
  onRemove 
}) => {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id });
  
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    cursor: 'grab',
  };
  
  // Get background color based on service status
  const getServiceStatusColor = () => {
    switch (serviceStatus) {
      case 'above':
        return 'bg-red-500';
      case 'average':
        return 'bg-amber-500';
      case 'below':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };
  
  return (
    <div 
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`flex items-center justify-between p-1 mb-1 rounded text-xs ${
        isHighlighted ? 'bg-amber-100' : 'bg-gray-100'
      } hover:bg-gray-200`}
    >
      <div className="flex items-center gap-1 truncate">
        <span className="truncate">{name}</span>
        {serviceCount > 0 && (
          <div className={`${getServiceStatusColor()} text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-semibold ml-1`}>
            {serviceCount}
          </div>
        )}
      </div>
      <button 
        onClick={(e) => {
          e.stopPropagation();
          onRemove();
        }}
        className="ml-1 text-red-500 hover:text-red-700"
      >
        &times;
      </button>
    </div>
  );
};

const ScheduleForm = () => {
  const { scheduleState, addPersonToShift, removePersonFromShift, countAssignments, getScheduleForDay, updateShiftOrder, wasPersonScheduledRecently } = useSchedule();
  const [persons, setPersons] = useState<Person[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const tabsListRef = useRef<HTMLDivElement>(null);
  const location = useLocation();
  const loadedScheduleData = useRef(false);
  const [memberServiceStats, setMemberServiceStats] = useState<Record<string, MemberServiceStats>>({});
  const [averageServiceCount, setAverageServiceCount] = useState(0);
  const [schedulesData, setSchedulesData] = useState<any[]>([]);
  const { isMemberAvailableOnDate } = useScheduleValidation();
  
  // DND sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // 5px movement before drag starts
      },
    })
  );

  // Function to get current semester date range
  const getCurrentSemesterDates = () => {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    
    // First semester: January to June
    // Second semester: July to December
    const isFirstSemester = currentMonth < 6;
    
    const startDate = isFirstSemester 
      ? new Date(currentYear, 0, 1) // Jan 1
      : new Date(currentYear, 6, 1); // Jul 1
      
    const endDate = isFirstSemester
      ? new Date(currentYear, 5, 30) // Jun 30
      : new Date(currentYear, 11, 31); // Dec 31
      
    return { startDate, endDate };
  };
  
  // Fetch member service statistics from previous schedules
  const fetchMemberServiceStats = async () => {
    try {
      // Get current semester date range
      const { startDate, endDate } = getCurrentSemesterDates();
      
      // Fetch past schedules
      const { data: fetchedSchedulesData, error } = await supabase
        .from('escalasPassadas')
        .select('*');

      if (error) throw error;

      // Store schedules data for priority calculation
      setSchedulesData(fetchedSchedulesData || []);
      
      // Calculate service counts for each member
      const counts: Record<string, number> = {};
      let totalServices = 0;
      let activeMemberCount = 0;
      
      // Function to parse dates in format DD/MM/YYYY
      const parseBrazilianDate = (dateStr?: string) => {
        if (!dateStr) return null;
        const parts = dateStr.split('/');
        if (parts.length !== 3) return null;
        
        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10) - 1; // JS months are 0-indexed
        const year = parseInt(parts[2], 10);
        
        return new Date(year, month, day);
      };
      
      // Count services in the current semester
      if (fetchedSchedulesData) {
        fetchedSchedulesData.forEach(schedule => {
          const scheduleDate = parseBrazilianDate(schedule.Data);
          
          // Only count services from the current semester
          if (scheduleDate && scheduleDate >= startDate && scheduleDate <= endDate) {
            // Count for each member position
            ['Lider', 'Membro1', 'Membro2', 'Membro3', 'Membro4'].forEach(memberField => {
              const memberName = schedule[memberField as keyof typeof schedule];
              if (memberName && typeof memberName === 'string' && memberName.trim() !== '') {
                counts[memberName] = (counts[memberName] || 0) + 1;
                totalServices++;
              }
            });
          }
        });
      }
      
      // Count active members - Fix for the Status property
      persons.forEach(person => {
        // Check if Status exists and equals 'ATIVO' with a safer approach
        if (person && 'Status' in person && person.Status === 'ATIVO') {
          activeMemberCount++;
        }
      });
      
      // Calculate average services per member
      const average = activeMemberCount > 0 ? totalServices / activeMemberCount : 0;
      setAverageServiceCount(average);
      
      // Determine status for each member
      const stats: Record<string, MemberServiceStats> = {};
      
      Object.entries(counts).forEach(([name, count]) => {
        let status: ServiceFrequencyStatus = 'average';
        
        if (count > average * 1.2) {
          status = 'above';
        } else if (count < average * 0.8) {
          status = 'below';
        }
        
        stats[name] = { count, status };
      });
      
      setMemberServiceStats(stats);
    } catch (error) {
      console.error("Error fetching service statistics:", error);
    }
  };
  
  useEffect(() => {
    const loadPersons = async () => {
      try {
        const data = await fetchPersons();
        setPersons(Array.isArray(data) ? data : []);
      } catch (error) {
        console.error("Failed to load persons:", error);
        toast.error("Falha ao carregar os dados de pessoas");
        setPersons([]);
      } finally {
        setLoading(false);
      }
    };
    
    loadPersons();
  }, []);
  
  // Fetch service stats after persons are loaded
  useEffect(() => {
    if (persons.length > 0) {
      fetchMemberServiceStats();
    }
  }, [persons]);
  
  useEffect(() => {
    if (scheduleState.selectedDays && scheduleState.selectedDays.length > 0) {
      // Set the first selected day as active tab
      setActiveTab(format(scheduleState.selectedDays[0], 'yyyy-MM-dd'));
    } else {
      setActiveTab(null);
    }
  }, [scheduleState.selectedDays]);

  // Function to populate people from previous schedule when editing
  useEffect(() => {
    if (loading || loadedScheduleData.current) return;
    
    const populateEditingSchedule = async () => {
      if (location.state?.scheduleData?.scheduleData && Array.isArray(location.state.scheduleData.scheduleData)) {
        loadedScheduleData.current = true;
        
        try {
          // For each shift in the schedule being edited
          location.state.scheduleData.scheduleData.forEach(shiftData => {
            if (!shiftData.Data || !shiftData.Turno) return;
            
            // Convert date from DD/MM/YYYY to Date
            const dateStr = shiftData.Data;
            let dateObj: Date;
            
            try {
              // Try format DD/MM/YYYY
              dateObj = parse(dateStr, 'dd/MM/yyyy', new Date());
              if (isNaN(dateObj.getTime())) {
                throw new Error('Invalid date');
              }
            } catch (e) {
              console.error(`Invalid date format: ${dateStr}`);
              return;
            }
            
            // Map shift name to shift key
            let shiftKey: "morning" | "afternoon" | "evening";
            
            // Determine which shift based on name
            if (shiftData.Turno === "EBD") {
              shiftKey = "morning";
            } else if (shiftData.Turno === "1º Culto") {
              shiftKey = "afternoon";
            } else if (shiftData.Turno === "2º Culto") {
              shiftKey = "evening";
            } else if (shiftData.Turno === "Manhã") {
              shiftKey = "morning";
            } else if (shiftData.Turno === "Tarde") {
              shiftKey = "afternoon";
            } else if (shiftData.Turno === "Noite") {
              shiftKey = "evening";
            } else {
              console.error(`Unknown shift: ${shiftData.Turno}`);
              return;
            }
            
            // Add each person to the shift
            const personFields = [
              { key: 'Lider', value: shiftData.Lider },
              { key: 'Membro1', value: shiftData.Membro1 },
              { key: 'Membro2', value: shiftData.Membro2 },
              { key: 'Membro3', value: shiftData.Membro3 },
              { key: 'Membro4', value: shiftData.Membro4 },
              { key: 'Membro5', value: shiftData.Membro5 },
              { key: 'Membro6', value: shiftData.Membro6 },
              { key: 'Membro7', value: shiftData.Membro7 },
              { key: 'Membro8', value: shiftData.Membro8 },
              { key: 'Membro9', value: shiftData.Membro9 }
            ];
            
            // For each person field, find the corresponding ID and add to schedule
            personFields.forEach(field => {
              if (!field.value) return;
              
              // Find person by name
              const person = persons.find(p => p.Nome === field.value);
              if (person) {
                // Add person to shift
                addPersonToShift(dateObj, shiftData.Turno, person.ID);
              } else {
                console.warn(`Person not found: ${field.value}`);
              }
            });
          });
          
          toast.success('Successfully loaded people from previous schedule!');
        } catch (error) {
          console.error('Error populating people from schedule:', error);
          toast.error('Failed to load people from previous schedule');
        }
      }
    };
    
    if (persons.length > 0) {
      populateEditingSchedule();
    }
  }, [location.state, persons, loading, addPersonToShift]);
  
  // Function to get shift labels based on day of week
  const getShiftLabelsForDay = (dayOfWeek: DayOfWeek) => {
    if (dayOfWeek === "Domingo") {
      return [
        { key: "morning", label: "EBD" },
        { key: "afternoon", label: "1º Culto" },
        { key: "evening", label: "2º Culto" }
      ];
    } else {
      return [
        { key: "morning", label: "Manhã" },
        { key: "afternoon", label: "Tarde" },
        { key: "evening", label: "Noite" }
      ];
    }
  };
  
  // Function to get available persons for a specific day and shift
  // Calculate priority statistics for member ordering
  const calculateMemberPriority = (memberName: string, schedulesData: any[]) => {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const threeMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 3, 1);

    // Helper function to parse Brazilian date format (DD/MM/YYYY)
    const parseBrazilianDate = (dateStr: string) => {
      if (!dateStr || typeof dateStr !== 'string') return null;
      const [day, month, year] = dateStr.split('/').map(Number);
      if (!day || !month || !year) return null;
      return new Date(year, month - 1, day);
    };

    let lastMonthCount = 0;
    let threeMonthCount = 0;

    // Count schedules for this member
    schedulesData.forEach(schedule => {
      const scheduleDate = parseBrazilianDate(schedule.Data);
      if (!scheduleDate) return;

      // Check if this member appears in any position
      const memberPositions = [
        schedule.Lider,
        schedule.Membro1,
        schedule.Membro2,
        schedule.Membro3,
        schedule.Membro4,
        schedule.Membro5,
        schedule.Membro6,
        schedule.Membro7,
        schedule.Membro8,
        schedule.Membro9
      ].filter(Boolean);

      if (memberPositions.includes(memberName)) {
        // Count for last month
        if (scheduleDate >= lastMonth) {
          lastMonthCount++;
        }

        // Count for last 3 months
        if (scheduleDate >= threeMonthsAgo) {
          threeMonthCount++;
        }
      }
    });

    // Determine priority based on scheduling history
    let priority = 4; // Default: others
    let priorityLabel = 'Outros';

    if (lastMonthCount === 0) {
      priority = 1;
      priorityLabel = 'Não escalado mês anterior';
    } else if (threeMonthCount === 1) {
      priority = 2;
      priorityLabel = '1x em 3 meses';
    } else if (threeMonthCount === 2) {
      priority = 3;
      priorityLabel = '2x em 3 meses';
    }

    return {
      priority,
      lastMonthCount,
      threeMonthCount,
      priorityLabel
    };
  };

  // Hook to get unavailable members for all dates we need
  const { data: allUnavailableMembers } = useQuery({
    queryKey: ['all-unavailable-members'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('disponibilidade_membros')
        .select('membro_nome, data_indisponivel')
        .gte('data_indisponivel', new Date().toISOString().split('T')[0]);

      if (error) {
        console.error('Erro ao buscar indisponibilidades:', error);
        return [];
      }

      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const getAvailablePersons = (dayOfWeek: DayOfWeek, shiftLabel: string, selectedDate: Date) => {
    if (!persons || !Array.isArray(persons) || persons.length === 0) {
      return [];
    }

    // Format date for API call
    const formattedDate = format(selectedDate, 'yyyy-MM-dd');

    // Create set of unavailable member names for this specific date
    const unavailableNames = new Set(
      (allUnavailableMembers || [])
        .filter(item => item.data_indisponivel === formattedDate)
        .map(item => item.membro_nome)
    );

    console.log(`📅 Data: ${formattedDate}, Indisponíveis: ${unavailableNames.size} pessoas`, Array.from(unavailableNames));

    return persons.filter(person => {
      if (!person || typeof person !== 'object') return false;

      const dayAvailability = person[dayOfWeek];
      if (!dayAvailability || typeof dayAvailability !== 'string') return false;

      // Check if the person is available for this shift
      const isAvailableForShift = dayAvailability.includes(shiftLabel);

      // Check if the person is NOT in the unavailable list
      const isAvailableOnDate = !unavailableNames.has(person.Nome);

      return isAvailableForShift && isAvailableOnDate;
    }).map(person => {
      // Get service stats if available for this person
      const stats = memberServiceStats[person.Nome] || { count: 0, status: 'average' as ServiceFrequencyStatus };

      // Calculate priority for this person
      const priority = calculateMemberPriority(person.Nome, schedulesData);

      return {
        value: person.ID,
        label: `${person.Nome} ${stats.count > 0 ? `(${stats.count})` : ''} ${priority.priorityLabel ? `[${priority.priorityLabel}]` : ''}`,
        count: stats.count,
        status: stats.status,
        priority: priority.priority,
        priorityInfo: priority
      };
    }).sort((a, b) => {
      // Sort by priority (lower priority number = higher priority)
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }

      // If same priority, sort by service count (ascending - fewer services first)
      if (a.count !== b.count) {
        return a.count - b.count;
      }

      // If same count, sort alphabetically
      return a.label.localeCompare(b.label);
    });
  };

  // Fallback function for client-side filtering (original logic)
  const getAvailablePersonsClientSide = (dayOfWeek: DayOfWeek, shiftLabel: string, selectedDate: Date) => {
    const formattedDate = format(selectedDate, 'yyyy-MM-dd');

    return persons.filter(person => {
      if (!person || typeof person !== 'object') return false;

      const dayAvailability = person[dayOfWeek];
      if (!dayAvailability || typeof dayAvailability !== 'string') return false;

      // Check if the person is available for this shift
      const isAvailableForShift = dayAvailability.includes(shiftLabel);

      // Check if the person is available on this specific date
      const isAvailableOnDate = isMemberAvailableOnDate(person.Nome, formattedDate);

      return isAvailableForShift && isAvailableOnDate;
    }).map(person => {
      // Get service stats if available for this person
      const stats = memberServiceStats[person.Nome] || { count: 0, status: 'average' as ServiceFrequencyStatus };

      // Calculate priority for this person
      const priority = calculateMemberPriority(person.Nome, schedulesData);

      return {
        value: person.ID,
        label: `${person.Nome} ${stats.count > 0 ? `(${stats.count})` : ''} ${priority.priorityLabel ? `[${priority.priorityLabel}]` : ''}`,
        count: stats.count,
        status: stats.status,
        priority: priority.priority,
        priorityInfo: priority
      };
    }).sort((a, b) => {
      // Sort by priority (lower priority number = higher priority)
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }

      // If same priority, sort by service count (ascending - fewer services first)
      if (a.count !== b.count) {
        return a.count - b.count;
      }

      // If same count, sort alphabetically
      return a.label.localeCompare(b.label);
    });
  };
  
  // Function to get currently assigned persons for a specific date and shift
  const getAssignedPersons = (date: Date, shiftKey: "morning" | "afternoon" | "evening") => {
    const schedule = getScheduleForDay(date);
    if (!schedule || !schedule.shifts || !schedule.shifts[shiftKey]) return [];
    
    const assignedIds = schedule.shifts[shiftKey];
    if (!Array.isArray(assignedIds)) return [];
    
    return assignedIds.map(personId => {
      const person = persons.find(p => p.ID === personId);
      const personName = person ? person.Nome : `Unknown (${personId})`;
      const isRecentlyAssigned = wasPersonScheduledRecently(personId, date);
      const stats = memberServiceStats[personName] || { count: 0, status: 'average' as ServiceFrequencyStatus };
      
      return {
        value: personId,
        label: personName,
        highlight: isRecentlyAssigned,
        count: stats.count,
        status: stats.status
      };
    });
  };
  
  // Check if a person has reached the maximum assignments
  const hasReachedMaxAssignments = (personId: string): boolean => {
    return countAssignments(personId) >= scheduleState.serviceLimits;
  };
  
  const handlePersonSelection = (date: Date, shiftKey: "morning" | "afternoon" | "evening", shiftLabel: string, selectedIds: string[]) => {
    const schedule = getScheduleForDay(date);
    const currentIds = schedule ? schedule.shifts[shiftKey] : [];
    
    // Ensure arrays are defined
    const safeSelectedIds = Array.isArray(selectedIds) ? selectedIds : [];
    const safeCurrentIds = Array.isArray(currentIds) ? currentIds : [];
    
    // Find IDs to add and remove
    const toAdd = safeSelectedIds.filter(id => !safeCurrentIds.includes(id));
    const toRemove = safeCurrentIds.filter(id => !safeSelectedIds.includes(id));
    
    // Process additions
    toAdd.forEach(id => {
      addPersonToShift(date, shiftLabel, id);
    });
    
    // Process removals
    toRemove.forEach(id => {
      removePersonFromShift(date, shiftLabel, id);
    });
  };
  
  // Handle drag end for reordering people in shifts
  const handleDragEnd = (event: DragEndEvent, date: Date, shiftKey: "morning" | "afternoon" | "evening") => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      const schedule = getScheduleForDay(date);
      if (!schedule) return;
      
      const personIds = [...schedule.shifts[shiftKey]];
      const oldIndex = personIds.indexOf(active.id.toString());
      const newIndex = personIds.indexOf(over.id.toString());
      
      if (oldIndex !== -1 && newIndex !== -1) {
        const newOrder = arrayMove(personIds, oldIndex, newIndex);
        updateShiftOrder(date, shiftKey, newOrder);
      }
    }
  };

  const scrollTabsLeft = () => {
    if (tabsListRef.current) {
      tabsListRef.current.scrollBy({ left: -100, behavior: 'smooth' });
    }
  };

  const scrollTabsRight = () => {
    if (tabsListRef.current) {
      tabsListRef.current.scrollBy({ left: 100, behavior: 'smooth' });
    }
  };
  
  if (loading) {
    return <Loading size="sm" text="Carregando dados de pessoas..." />;
  }
  
  if (!scheduleState.selectedDays || scheduleState.selectedDays.length === 0) {
    return (
      <div className="text-center py-8">
        <p>Selecione pelo menos um dia para criar ou editar a escala.</p>
      </div>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Escalas para os dias selecionados</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab || undefined} onValueChange={setActiveTab}>
          <div className="relative w-full mb-4">
            <Button 
              variant="ghost" 
              size="icon" 
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 h-8 w-8 bg-background/80 rounded-full shadow-sm"
              onClick={scrollTabsLeft}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div 
              ref={tabsListRef}
              className="overflow-x-auto scrollbar-none px-8"
              style={{ scrollBehavior: 'smooth', msOverflowStyle: 'none', scrollbarWidth: 'none' }}
            >
              <TabsList className="w-max">
                {scheduleState.selectedDays.map(day => (
                  <TabsTrigger 
                    key={format(day, 'yyyy-MM-dd')} 
                    value={format(day, 'yyyy-MM-dd')}
                    className="flex-shrink-0"
                  >
                    {format(day, 'd/MM', { locale: ptBR })}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>
            <Button 
              variant="ghost" 
              size="icon" 
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 h-8 w-8 bg-background/80 rounded-full shadow-sm"
              onClick={scrollTabsRight}
            >
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
          
          {scheduleState.selectedDays.map(date => {
            const dateKey = format(date, 'yyyy-MM-dd');
            const dayOfWeek = getDayOfWeek(date);
            const shiftLabels = getShiftLabelsForDay(dayOfWeek);
            
            return (
              <TabsContent key={dateKey} value={dateKey} className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-4">
                    {format(date, "EEEE, dd 'de' MMMM", { locale: ptBR }).replace(/^\w/, (c) => c.toUpperCase())}
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {shiftLabels.map(({ key, label }) => {
                      const typedKey = key as "morning" | "afternoon" | "evening";
                      const availablePersons = getAvailablePersons(dayOfWeek, label, date);
                      const assignedPersons = getAssignedPersons(date, typedKey);
                      
                      // Filter out persons who have reached the max assignments
                      // but keep already assigned persons
                      const eligiblePersons = availablePersons.filter(person => 
                        !hasReachedMaxAssignments(person.value) || 
                        assignedPersons.some(p => p.value === person.value)
                      );
                      
                      const assignmentCount = assignedPersons.length;
                      
                      return (
                        <div key={key} className="border rounded-md p-4 h-full">
                          <div className="flex items-center gap-2 mb-2">
                            <Label className="text-base w-16 min-w-[4rem] whitespace-nowrap">{label}</Label>
                            <Badge variant={assignmentCount > 0 ? "default" : "outline"}>
                              {assignmentCount} / 10
                            </Badge>
                          </div>
                          
                          <MultiSelect
                            options={eligiblePersons}
                            selectedValues={assignedPersons}
                            onChange={selected => 
                              handlePersonSelection(date, typedKey, label, selected)
                            }
                            placeholder="Selecione as pessoas"
                            disabled={eligiblePersons.length === 0}
                            maxItems={10}
                            highlightRecent={true}
                          />
                          
                          {eligiblePersons.length === 0 && (
                            <p className="text-sm text-muted-foreground mt-1">
                              Não há pessoas disponíveis para este turno.
                            </p>
                          )}
                          
                          {assignedPersons.length > 0 && (
                            <div className="mt-3 border-t pt-2">
                              <p className="text-xs text-muted-foreground mb-1">
                                Arraste para reordenar:
                              </p>
                              <DndContext 
                                sensors={sensors} 
                                onDragEnd={(event) => handleDragEnd(event, date, typedKey)}
                              >
                                <SortableContext 
                                  items={assignedPersons.map(p => p.value)} 
                                  strategy={verticalListSortingStrategy}
                                >
                                  {assignedPersons.map((person) => (
                                    <SortableItem
                                      key={person.value}
                                      id={person.value}
                                      name={person.label}
                                      isHighlighted={person.highlight || false}
                                      serviceCount={person.count}
                                      serviceStatus={person.status}
                                      onRemove={() => removePersonFromShift(date, label, person.value)}
                                    />
                                  ))}
                                </SortableContext>
                              </DndContext>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </TabsContent>
            );
          })}
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ScheduleForm;
