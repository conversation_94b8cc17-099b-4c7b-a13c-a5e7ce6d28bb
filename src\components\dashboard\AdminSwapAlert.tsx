
import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Shield, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface AdminSwapAlertProps {
  pendingCount: number;
  withCandidatesCount: number;
}

export const AdminSwapAlert = ({ pendingCount, withCandidatesCount }: AdminSwapAlertProps) => {
  const navigate = useNavigate();

  if (pendingCount === 0 && withCandidatesCount === 0) return null;

  return (
    <Alert className="border-blue-200 bg-blue-50 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Shield className="h-5 w-5 text-blue-600" />
          <div>
            <AlertTitle className="text-blue-800 text-sm font-medium">Acesso Administrativo</AlertTitle>
            <AlertDescription className="text-blue-700 text-sm">
              {pendingCount} pendentes{withCandidatesCount > 0 && `, ${withCandidatesCount} prontas para aprovação`}
            </AlertDescription>
          </div>
        </div>
        <Button
          onClick={() => navigate('/admin-swap-requests')}
          size="sm"
          className="flex items-center gap-2"
        >
          Gerenciar
          <ArrowRight className="w-4 h-4" />
        </Button>
      </div>
    </Alert>
  );
};
