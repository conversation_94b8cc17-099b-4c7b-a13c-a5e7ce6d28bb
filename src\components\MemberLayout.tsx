
import React from 'react';
import { SidebarProvider, SidebarInset, SidebarTrigger } from '@/components/ui/sidebar';
import { MemberSidebar } from './MemberSidebar';
import { MemberNotificationBell } from './MemberNotificationBell';

interface MemberLayoutProps {
  children: React.ReactNode;
}

export const MemberLayout = ({ children }: MemberLayoutProps) => {
  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full">
        <MemberSidebar />
        <SidebarInset>
          {/* Header sempre visível */}
          <header className="flex h-16 shrink-0 items-center justify-between gap-2 border-b bg-background px-4">
            <div className="flex items-center gap-2">
              <SidebarTrigger className="md:hidden" />
              <h1 className="text-lg font-semibold">Portal do Membro</h1>
            </div>
            <MemberNotificationBell />
          </header>
          <main className="flex-1 p-4">
            {children}
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};
